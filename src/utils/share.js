import { log } from 'commonkit'
import { copy } from '@/utils/clipboard'
import { getFuLiHuiID } from '@api/interface/flh.js'
import { getBizCode } from '@/utils/curEnv'
import shared from '@/shared'
import { curDeveloperId } from '@utils/storage.js'

export const getDefaultShareUrl = async () => {
  const urlSearch = window.location.search
  const urlQueryStr = urlSearch ? urlSearch.split('?')[1] : ''

  let url = window.location.origin + window.location.pathname + urlSearch
  if (!(urlQueryStr.indexOf('distri_biz_code') >= 0)) {
    url += url.indexOf('?') >= 0 ? '&' : '?'
    // 福利汇商城分享
    const bizCode = getBizCode()
    if (bizCode === 'fulihui') {
      let developerId = curDeveloperId.get()
      if (!developerId) {
        const [err, json] = await getFuLiHuiID({ bizCode })
        if (!err) {
          developerId = json || ''
        }
      }
      curDeveloperId.set(developerId)

      const shareUrl = url + 'distri_biz_code=' + bizCode + '&developerId=' + developerId
      log('[SHARE-DATA] 福利汇 shareUrl', shareUrl)
      return shareUrl
    }
    const shareUrl = url + 'distri_biz_code=' + bizCode
    log('[SHARE-DATA] shareUrl', shareUrl)
    return shareUrl
  }
  return url
}

// 分享数据
export const shareData = {
  link: '',
  title: '',
  describe: '',
  picUrl: '',
  next: (options, userData) => {
    // 当前渠道不支持分享，需要手工后续处理
    if (options.type === 'h5') {
      copy(userData, shareData.link)
    } else if (options.type === 'weixin') {
      shared.app.$toast('请点击屏幕右上方的按钮进行分享')
    }
  },
  callback: () => {
    // 分享完成后回调，不推荐使用，不是所有渠道都有回调
  }
}
