<template>
  <van-popup :show="visible" position="bottom" round @close="handleClose"
    @update:show="(value) => emit('update:visible', value)">
    <div class="spec-popup">
      <!-- 弹窗头部 -->
      <div class="popup-header">
        <h3 class="popup-title">选择规格</h3>
        <div class="close-btn" @click="handleClose">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <path d="M18 6L6 18M6 6L18 18" stroke="#999" stroke-width="2" stroke-linecap="round" />
          </svg>
        </div>
      </div>

      <!-- 商品信息区域 -->
      <div class="goods-info">
        <!-- <div class="goods-basic">
          <div class="address-info">
            <div class="address-content">
              <div class="address-detail">
                <span class="address-text">{{ addressText }}</span>
              </div>
              <div class="receiver-info" v-if="addressInfo.receiverName && addressInfo.receiverPhone">
                <span class="receiver-name">{{ addressInfo.receiverName || '收货人' }}</span>
                <span class="receiver-phone">{{ addressInfo.receiverPhone || '手机号' }}</span>
              </div>
            </div>
            <img src="../assets/arrow-gray.png" alt="箭头" class="arrow-right" />
          </div>
        </div> -->

        <div class="goods-detail">
          <img :src="goodsInfo.image" alt="商品图片" class="goods-image" />
          <div class="goods-content">
            <PriceDisplay :price="goodsInfo.price" size="large" color="orange" />
            <div class="quantity-section">
              <van-stepper v-model="quantity" :min="quantityRange.min" :max="quantityRange.max"
                :disabled="quantityRange.max === 0" @change="handleQuantityChange" />
              <div class="purchase-limit">{{ purchaseLimitText }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 规格选择区域 -->
      <div class="spec-section">
        <div class="spec-header">
          <span class="spec-title">规格</span>
          <div class="goods-code">
            <span class="code-label">商品编码：</span>
            <span class="code-value">{{ goodsInfo.supplierSkuId }}</span>
            <img src="../assets/copy.png" alt="复制" class="copy-icon" width="16" height="16" @click="handleCopyCode" />
          </div>
        </div>

        <div class="spec-options">
          <div class="radio-wrapper" v-for="(specs, groupIndex) in displaySpecsList" :key="groupIndex">
            <div class="spec-group-title" v-if="specs.length > 0">
              {{ getSpecGroupName(groupIndex) }}
            </div>
            <button v-for="(spec, specIndex) in specs" :key="specIndex"
              :class="{ active: specOptions.curSpecs.indexOf(spec) >= 0, disabled: specOptions.curDisabledSpecs.indexOf(spec) >= 0 }"
              @click="selectSpec(spec)">
              {{ removeSpecPrefix(spec) }}
            </button>
          </div>
        </div>
      </div>

      <!-- 底部操作栏 -->
      <WoActionBar>
        <div class="action-buttons">
          <!-- 没有规格时显示确定按钮 -->
          <WoButton v-if="!hasSpecs" block type="gradient" size="large" class="confirm-btn" 
            :disabled="cartButtonDisabled" @click="handleConfirm">
            确定
          </WoButton>
          <!-- 有规格时显示加入购物车和立即购买按钮 -->
          <template v-else>
            <WoButton type="gradient" size="large" class="add-cart-btn" 
              :disabled="cartButtonDisabled" @click="handleAddToCart">
              加入购物车
            </WoButton>
            <WoButton type="gradient" size="large" class="buy-now-btn" 
              :disabled="cartButtonDisabled" @click="handleBuyNow">
              立即购买
            </WoButton>
          </template>
        </div>
      </WoActionBar>
    </div>
  </van-popup>
</template>

<script setup>
import { ref, computed, watch, onMounted, nextTick } from 'vue'
import WoButton from '@/components/WoElementCom/WoButton/WoButton.vue'
import WoActionBar from '@/components/WoElementCom/WoActionBar.vue'
import PriceDisplay from '@/components/Common/PriceDisplay.vue'
import useClipboard from 'vue-clipboard3'
import { showToast } from 'vant'
import { removeSpecPrefix } from '@/utils/goodsDetail'
const { toClipboard } = useClipboard()

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  addressInfo: {
    type: Object,
    default: () => ({
      provinceName: '',
      cityName: '',
      countyName: '',
      townName: '',
      addrDetail: '',
      receiverName: '',
      receiverPhone: ''
    })
  },
  goodsInfo: {
    type: Object,
    default: () => ({
      image: '',
      price: 0,
      supplierSkuId: '',
      stock: 0,
      purchaseLimit: 9999,
      purchaseLimitType: 'none',
      purchaseLimitText: '',
      currSku: {},
      xgObj: {},
      lowestBuyObj: {}
    })
  },
  specOptions: {
    type: Object,
    default: () => ({
      specsList: [],
      curSpecs: [],
      curDisabledSpecs: []
    })
  },
  initialQuantity: {
    type: Number,
    default: 1
  },
  actionType: {
    type: Number,
    default: 1,
    validator: (value) => [1, 2].includes(value)
  },
  cartButtonDisabled: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:visible', 'select-spec', 'add-to-cart', 'buy-now', 'confirm', 'quantity-change'])

// 添加复制方法
const handleCopyCode = async () => {
  try {
    await toClipboard(props.goodsInfo.supplierSkuId)
    showToast('复制成功');
  } catch (error) {
    console.error('复制失败:', error)
    showToast('复制失败');
  }
}

// 响应式数据
const quantity = ref(props.initialQuantity)

// 计算属性
const addressText = computed(() => {
  const { provinceName, cityName, countyName, townName, addrDetail } = props.addressInfo
  const locationText = `${provinceName || ''} ${cityName || ''} ${countyName || ''} ${townName || ''}`.trim()
  const fullAddress = addrDetail ? `${locationText} ${addrDetail}` : locationText
  return fullAddress || '配送地址'
})

const purchaseLimitText = computed(() => {
  const { xgObj, lowestBuyObj, purchaseLimitText, stock } = props.goodsInfo

  const messages = []

  // 库存提示
  if (stock <= 0) {
    messages.push('暂无库存')
  }

  // 起购提示
  if (lowestBuyObj?.isLowestBuy) {
    messages.push(lowestBuyObj.lowestBuyText)
  }

  // 限购提示
  if (xgObj?.isXg && xgObj?.limitText) {
    messages.push(xgObj.limitText)
  }

  // 如果有自定义限制文本，优先使用
  if (purchaseLimitText) {
    messages.push(purchaseLimitText)
  }

  return messages.join('，')
})

// 计算有效的数量范围
const quantityRange = computed(() => {
  const { xgObj, lowestBuyObj, stock } = props.goodsInfo

  // 最小值：起购数量或1
  let min = 1
  if (lowestBuyObj?.isLowestBuy) {
    min = lowestBuyObj.lowestBuyNum
  }

  // 最大值：取限购、库存中的最小值
  let max = 999

  // 库存限制
  if (stock > 0) {
    max = Math.min(max, stock)
  }

  // 限购限制
  if (xgObj?.isXg && xgObj?.limitNum) {
    max = Math.min(max, xgObj.limitNum)
  }

  return { min, max }
})

// 监听商品信息变化，自动调整数量
watch(() => props.goodsInfo, (newGoodsInfo) => {
  const { min } = quantityRange.value

  // 如果当前数量小于最小起购量，自动调整
  if (quantity.value < min) {
    quantity.value = min
  }

  // 如果当前数量超过最大限制，自动调整
  const { max } = quantityRange.value
  if (quantity.value > max) {
    quantity.value = max
  }
}, { deep: true })

// 更新初始数量监听，考虑起购要求
watch(() => props.initialQuantity, (newVal) => {
  const { min } = quantityRange.value
  quantity.value = Math.max(newVal, min)
})


// 监听数量变化，向父组件发送事件
watch(quantity, (newVal) => {
  emit('quantity-change', newVal)
})

// 方法
const handleClose = () => {
  emit('update:visible', false)
}

const selectSpec = (spec) => {
  if (props.specOptions.curDisabledSpecs.indexOf(spec) >= 0) return

  // 如果是默认规格且已经选中，则不允许取消选择
  if (spec === '默认规格' && props.specOptions.curSpecs.indexOf(spec) >= 0) {
    return
  }

  emit('select-spec', spec)
}

// 更新数量变化处理方法，添加验证逻辑
const handleQuantityChange = (value) => {
  const { min, max } = quantityRange.value
  const { xgObj, lowestBuyObj, stock } = props.goodsInfo

  // 验证最小值（起购）
  if (value < min) {
    if (lowestBuyObj?.isLowestBuy) {
      showToast(`最少购买${min}件哦！`)
    }
    quantity.value = min
    return
  }

  // 验证最大值
  if (value > max) {
    if (stock > 0 && value > stock) {
      showToast(`库存不足，仅剩${stock}件`)
    } else if (xgObj?.isXg && value > xgObj.limitNum) {
      showToast(`超出限购数量：${xgObj.limitText}`)
    }
    quantity.value = max
    return
  }

  quantity.value = value
}

const handleAddToCart = () => {
  // 检查规格是否选择完整
  if (!isSpecsComplete()) {
    showToast('请选择完整商品规格！')
    return
  }

  const skuInfo = {
    quantity: quantity.value,
    selectedSpecs: props.specOptions.curSpecs,
    goodsId: props.goodsInfo.goodsId,
    supplierSkuId: props.goodsInfo.supplierSkuId,
    price: props.goodsInfo.price,
    stock: props.goodsInfo.stock,
    // 当前SKU信息
    currSku: props.goodsInfo.currSku,
  }
  emit('add-to-cart', skuInfo)
}

const handleBuyNow = () => {
  // 检查规格是否选择完整
  if (!isSpecsComplete()) {
    showToast('请选择完整商品规格！')
    return
  }

  const skuInfo = {
    quantity: quantity.value,
    selectedSpecs: props.specOptions.curSpecs,
    goodsId: props.goodsInfo.goodsId,
    supplierSkuId: props.goodsInfo.supplierSkuId,
    price: props.goodsInfo.price,
    stock: props.goodsInfo.stock,
    // 当前SKU信息
    currSku: props.goodsInfo.currSku,
  }
  emit('buy-now', skuInfo)
}

// 确定按钮处理方法 - 根据actionType区分行为
const handleConfirm = () => {
  // 检查规格是否选择完整
  if (!isSpecsComplete()) {
    showToast('请选择完整商品规格！')
    return
  }

  const skuInfo = {
    quantity: quantity.value,
    selectedSpecs: props.specOptions.curSpecs,
    goodsId: props.goodsInfo.goodsId,
    supplierSkuId: props.goodsInfo.supplierSkuId,
    price: props.goodsInfo.price,
    stock: props.goodsInfo.stock,
    // 当前SKU信息
    currSku: props.goodsInfo.currSku,
  }

  // 根据actionType决定触发哪个事件
  if (props.actionType === 2) {
    emit('buy-now', skuInfo)
  } else {
    emit('add-to-cart', skuInfo)
  }
}

// 计算属性：判断是否有规格
const hasSpecs = computed(() => {
  return props.specOptions && props.specOptions.specsList && props.specOptions.specsList.length > 0 && props.specOptions.specsList[0].length > 0
})

// 计算属性：显示的规格列表（如果没有规格则添加默认规格）
const displaySpecsList = computed(() => {
  if (hasSpecs.value) {
    return props.specOptions.specsList
  } else {
    // 没有规格时，添加默认规格
    return [['默认规格']]
  }
})

// 获取规格分组名称
const getSpecGroupName = (groupIndex) => {
  // 如果没有规格，显示默认
  if (!hasSpecs.value) {
    return '规格'
  }
  
  // 根据索引生成规格组名称
  return `规格${groupIndex + 1}`
}

// 检查规格是否选择完整
const isSpecsComplete = () => {
  const specsListArr = props.specOptions.specsList || []
  const curSpecs = props.specOptions.curSpecs || []
  
  // 如果没有规格，认为是完整的
  if (!hasSpecs.value) {
    return true
  }
  
  // 计算有效的规格组数量（非空的规格组）
  const validSpecsGroupCount = specsListArr.reduce((count, group) => {
    if (group && group.length > 0) count++
    return count
  }, 0)
  
  // 当前选择的规格数量应该等于有效规格组数量
  return validSpecsGroupCount === curSpecs.length
}

// 监听弹窗显示状态，确保每次打开时都检查默认规格选中状态
watch(() => props.visible, (newVisible) => {
  if (newVisible) {
    // 弹窗打开时，延迟检查并自动选中默认规格
    nextTick(() => {
      if (!hasSpecs.value && props.specOptions.curSpecs.length === 0) {
        emit('select-spec', '默认规格')
      }
    })
  }
})


</script>

<style scoped lang="less">
.spec-popup {
  background-color: @bg-color-white;
  border-radius: @radius-12 @radius-12 0 0;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  padding-bottom: 70px;

  .popup-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    flex-shrink: 0;

    .popup-title {
      font-size: @font-size-16;
      color: @text-color-primary;
      font-weight: @font-weight-500;
      margin: 0;
    }

    .close-btn {
      width: 24px;
      height: 24px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .goods-info {
    padding: 0 17px 10px 17px;
    flex-shrink: 0;
    box-sizing: border-box;

    .goods-basic {
      margin-bottom: 16px;

      .address-info {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 4px;

        .address-content {
          flex: 1;

          .receiver-info {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 4px;

            .receiver-name {
              font-size: @font-size-14;
              color: @text-color-secondary;
              font-weight: @font-weight-400;
            }

            .receiver-phone {
              font-size: @font-size-14;
              color: @text-color-secondary;
              font-weight: @font-weight-400;
            }
          }

          .address-detail {
            .address-text {
              font-size: @font-size-15;
              color: @text-color-primary;
              font-weight: @font-weight-600;
              line-height: 1.4;
            }
          }
        }

        .arrow-right {
          width: 6px;
          height: 12px;
          flex-shrink: 0;
          margin-left: 8px;
        }
      }
    }

    .goods-detail {
      display: flex;
      gap: 12px;

      .goods-image {
        width: 80px;
        height: 80px;
        border-radius: @radius-8;
        object-fit: cover;
        flex-shrink: 0;
      }

      .goods-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .price-display {
          margin-bottom: 8px;
        }

        .quantity-section {
          display: flex;
          align-items: center;
          justify-content: space-between;



          .purchase-limit {
            font-size: @font-size-13;
            color: #ff6b35;
          }
        }
      }
    }
  }

  .spec-section {
    padding: 10px 17px 0 17px;
    flex: 1;
    overflow-y: auto;
    box-sizing: border-box;

    .spec-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 16px;

      .spec-title {
        font-size: @font-size-16;
        color: @text-color-primary;
        font-weight: @font-weight-500;
      }

      .goods-code {
        display: flex;
        align-items: center;
        gap: 4px;

        .code-label {
          font-size: @font-size-12;
          color: #999;
        }

        .code-value {
          font-size: @font-size-12;
          color: #999;
        }

        .copy-icon {
          width: 10px;
          height: 10px;
          flex-shrink: 0;
          margin-left: 2px;
          cursor: pointer;
        }
      }
    }

    .spec-options {
      flex: 1;
      overflow-y: scroll;
      max-height: 300px;
      min-height: 150px;

      .radio-wrapper {
        line-height: 40px;

        .spec-group-title {
          font-size: @font-size-14;
          color: @text-color-primary;
          font-weight: @font-weight-500;
          margin-bottom: 8px;
          margin-top: 16px;
          
          &:first-child {
            margin-top: 0;
          }
        }

        .specs-button-division-line {
          width: 100%;
          height: 1px;
          background: rgba(229, 229, 229, 0.64);
        }

        &:last-child .specs-button-division-line {
          height: 0;
        }

        button {
          display: inline-block;
          min-width: 75px;
          font-size: @font-size-13;
          color: @text-color-primary;
          line-height: 1.2;
          background: #F7F7F7;
          border-radius: 4px;
          padding: 8px 12px;
          margin-right: 8px;
          margin-bottom: 8px;
          outline: none;
          border: 1px solid transparent;
          cursor: pointer;
          transition: all 0.2s ease;

          &.active {
            background: rgba(255, 120, 10, 0.10);
            border: 1px solid #FF780A;
            color: #FF780A;
          }

          &.disabled {
            background: #F7F7F7;
            color: #B1BEC9;
            cursor: not-allowed;
          }

          // &:hover:not(.disabled):not(.active) {
          //   border-color: #FF780A;
          //   color: #FF780A;
          // }
        }
      }
    }
  }

  .action-buttons {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .add-cart-btn {
      background-image: linear-gradient(90deg, #FFC72D 0%, #FFAD1B 100%);
    }

    .buy-now-btn {
      background-image: linear-gradient(101deg, #FFA033 0%, #FF6D33 100%);
    }
  }
}
</style>
